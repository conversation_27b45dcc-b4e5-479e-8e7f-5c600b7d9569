import { Environments } from "@apply4u/shared/enums/environments";

export const environment = {
  production: false,
    // BaseUrl:'http://localhost:31441/',
  BaseUrl:'https://stage2-api.apply4u.co.uk/',
// BaseUrl:'http://stage-api.apply4u.co.uk/',
  //  BaseUrl:'https://api.apply4u.co.uk/',
  //BaseUrl:'http://www.advertise4u.co.uk/'
  // clientSideBaseUrl:'http://stage-web.apply4u.co.uk/',
  //clientSideBaseUrl:'https://www.apply4u.co.uk/',
  HostName:'http://localhost:4200',
  clientSideBaseUrl:'http://localhost:4200/',
  EnvironmentId: Environments.Local,
 TransmitterApiBaseUrl: 'https://stage2-transmitter.apply4u.co.uk/',
  IdentityServerUrl : 'https://stage2-secure.apply4u.co.uk/',
  // TransmitterApiBaseUrl: 'https://localhost:7256/',
  // IdentityServerUrl : 'https://localhost:44341/',
  appInsights: {
    instrumentationKey: 'd8413f3c-6a11-47ad-bc49-84e7ba3c91e2',
    isEnabled:false
  }
};