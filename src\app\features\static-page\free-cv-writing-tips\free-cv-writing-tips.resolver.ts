import { Injectable } from "@angular/core";
import { BreadCrumbs, ListItem } from "@apply4u/models/seo/bread-crumbs";
import { ActivatedRouteSnapshot, RouterStateSnapshot } from "@angular/router";
import { MetaWrapperService } from "@apply4u/services/meta-wrapper.service";
import { GetURLWithoutQueryParams } from "@apply4u/shared/helpers/seo-helper";
import { LiveHostName, Shared } from "@apply4u/models/shared";

@Injectable({
  providedIn: 'root'
})
export class FreeCvWrittingTipsResolver  {

  constructor(private metaService: MetaWrapperService) {
  }

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): void {
    this.metaService.CreateCanonicalURL(GetURLWithoutQueryParams(`${LiveHostName}${state.url}`));
    this.metaService.SetPageTitle("CV Writing Tips | How to write a CV | Apply4U");
    this.metaService.UpdateMetaTag('description', "Professional CV writing is not just sending the employer a bunch of bullet points. It needs Research, gathering information, proof reading and much more to present yourself professionally on paper");
    this.AddBreadCrumbsScript();
  }
  
  AddBreadCrumbsScript(): void {
    const breadcrumbSchema = new BreadCrumbs();
    let listItem = new ListItem();
    listItem.position = 1;
    listItem.name = "Home";
    listItem.item = `${Shared.ClientSideUrl}`;

    breadcrumbSchema.itemListElement.push(listItem);
    let listItem2 = new ListItem();
    listItem2.position = 2;
    listItem2.name = "free-cv-writing-tips";
    listItem2.item = `${Shared.ClientSideUrl}${"https://www.apply4u.co.uk/free-cv-writing-tips"}`;
    breadcrumbSchema.itemListElement.push(listItem2);
    let breadcrumbSchemaString = JSON.stringify(breadcrumbSchema);
    this.metaService.CreateBreadCrumbsScript(breadcrumbSchemaString);
  }

}
