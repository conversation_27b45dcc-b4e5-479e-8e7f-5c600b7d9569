import { Component, OnInit} from '@angular/core';
import { BreadCrumbs, ListItem } from '@apply4u/models/seo/bread-crumbs';
import { MetaWrapperService } from '@apply4u/services/meta-wrapper.service';
import { CareerAdvice_RouteUrl } from '@apply4u/shared/constant/navigation/navigation';
import { environment } from '@environment/environment';

@Component({
  selector: 'app-free-cv-wrritting-tips',
  templateUrl: './free-cv-wrritting-tips.component.html',
  styleUrls: ['./free-cv-wrritting-tips.component.css']
})

export class FreeCvWrrittingTipsComponent implements OnInit {
  BreadCrumbSchema = new BreadCrumbs();

  constructor(private metaService: MetaWrapperService) {
  }

  ngOnInit(): void {
    this. PopulateBreadCrumbsDisplayList();
  }

  PopulateBreadCrumbsDisplayList(): void {
    let listItem = new ListItem();
    listItem.position = 1;
    listItem.name = "Career Advice Home";
    listItem.item = `${environment.HostName}${CareerAdvice_RouteUrl}`;
    this.BreadCrumbSchema.itemListElement.push(listItem);
   
    let listItem2 = new ListItem();
    listItem2.position = 2;
    listItem2.name = "Your CV";
    listItem2.item = `${environment.HostName}${"/your-curriculum-vitae"}`;
    this.BreadCrumbSchema.itemListElement.push(listItem2);
   
    let listItem3 = new ListItem();
    listItem3.position = 3;
    listItem3.name = "CV Writing Tips";
    listItem3.item = `${environment.HostName}${"https://www.apply4u.co.uk/free-cv-writing-tips"}`;
    this.BreadCrumbSchema.itemListElement.push(listItem3);

    let breadcrumbSchemaString = JSON.stringify(this.BreadCrumbSchema); 
    this.metaService.CreateBreadCrumbsScript(breadcrumbSchemaString);
     }
}
