.CvTemplate h2 {
  margin: 0;
  padding: 0;
  width: 100%;
  font-size: 18px;
  color: #223a6b;
  margin-bottom: 10px;
  font-weight: 600 !important;
}

.CvTemplate p {
  font-size: 16px;
 margin-bottom: 10px;
  text-align: left;
}

.CvTemplate p a {
  color: #0e1b5d;
  font-weight: 500;
  text-decoration: underline;
}
.CvTemplate p a:hover {
  color: #0e1b5d;
font-weight: 500;
text-decoration: none;
}

.CvTemplate img {
  width: 100%;
  height: auto;
  margin-bottom: 15px;
  border-radius: 10px;
}

.card-header .title {
  font-size: 16px;
  color: #223a6b;
  font-weight: 500 !important;
}
.CvTemplate .accordion .card-body {
  text-align: center;
}
.CvTemplate .accordion .card-body p{
  text-align: center;
}

.card-header .accicon {
  float: right;
  font-size: 20px;
  width: 1.2em;
}

.card-header {
  cursor: pointer;
  border-bottom: none;
}

.card {
  border: 1px solid #ddd;
}

.card-body {
  border-top: 1px solid #ddd;
}

.card-header:not(.collapsed) .rotate-icon {
  transform: rotate(180deg);
}

.tab-content p span {
  font-size: 16px;
  color: #223a6b;
  font-weight: 500 !important;
}
.EbookDownload{
  padding: 15px;
  display: block;
  margin:15px 0px;
  overflow: hidden;
  border-radius: 8px;
  background:#D1FCDD;
  /* background:#F3F3F3; */
  border:1px solid #42B865;
}
.EbookDownload span.filename{
  width: 50%;
  float: left;
  font-size:14px;
}
.EbookDownload span.filename i{
  font-size: 30px;
}
.EbookDownload span.download{
  width: 50%;
  float: right;
  text-align: right;
}
.boldtxt{
  font-weight: 500;
}