trigger: none

pool:
  vmImage: 'windows-latest'

variables:
  - group: a4udev
  - name: version.MajorMinor
    value: '1.0'
  - name: version.Revision
    value: $[counter(variables['version.MajorMinor'], 0)]
  - name: versionNumber
    value: $[format('{0}.{1}', variables['version.MajorMinor'], variables['version.Revision'])]
  - name: versionNumberHyphenated
    value: $[replace(variables['versionNumber'], '.', '-')]
  - name: buildConfiguration
    value: 'Release'
  - name: buildPlatform
    value: 'Any CPU'

stages:
  - stage: BuildDev
    displayName: BuildDev
    variables:
    - group: a4udev
    jobs:
    - job: BuildDev
      displayName: 'BuildDev'
      steps:
      - template: templates/web-build-dev-template.yml
  
  - stage: DeployDev
    displayName: DeployToDev
    dependsOn: BuildDev
    variables:
    - group: a4udev
    jobs:
    - deployment: Deploy
      displayName: 'DeployDev'
      environment: Dev
      strategy:
        runOnce:
          deploy:
            steps:
            - template: templates/web-release-template.yml
              parameters:
                serviceConnectionName: $(serviceConnectionName)
                webAppSVNRepoUrl: $(webAppSVNRepoUrl)
                svnUsername: $(svnUsername)
                svnPassword: $(svnPassword)

  - stage: UpdateDevEnvironment
    displayName: UpdateDevServer
    dependsOn: DeployDev
    variables:
    - group: a4udev
    jobs:
    - deployment: Deploy
      displayName: 'UpdateDevEnvironment'
      environment: Dev
      strategy:
        runOnce:
          deploy:
            steps:
            - template: templates/web-updateserver-template.yml
              parameters:
                serviceConnectionName: $(serviceConnectionName)
                webAppWorkingDirectory: $(webAppWorkingDirectory)
                serverIP: $(serverIP)
                serverUsername: $(serverUsername)
                serverPassword: $(serverPassword)
                webAppPoolName: $(webAppPoolName)