/* Apply4U Brand Theme Contact Us Design */

/* Reset any conflicting styles */
.modern-contact-wrapper,
.contact-info-panel,
.contact-form-panel,
.elegant-form,
.elegant-input,
.elegant-textarea,
.elegant-submit-btn,
.section-subtitle,
.section-title,
.section-description,
.contact-detail-item,
.icon-circle,
.detail-content,
.social-links {
  display: none !important;
}

/* Force display of new elements */
.clean-modern-contact,
.contact-info-card,
.contact-form-card,
.modern-contact-form,
.modern-input,
.modern-textarea,
.modern-submit-btn {
  display: block !important;
}

.modern-contact-form {
  display: flex !important;
}

/* Main Contact Section */
.clean-modern-contact {
  background: #f6faff !important;
  padding: 4rem 0 !important;
  display: block !important;
  visibility: visible !important;
}

/* Contact Information Card - Apply4U Brand Theme */
.contact-info-card {
  background: #0e1b5d !important;
  padding: 2rem 2.5rem !important;
  border-radius: 12px !important;
  height: 100% !important;
  box-shadow: 0 4px 20px rgba(14, 27, 93, 0.3) !important;
  color: white !important;
  display: flex !important;
  flex-direction: column !important;
  visibility: visible !important;
}

/* Header Section */
.contact-header {
  margin-bottom: 1.5rem;
}

.contact-us-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #14C59C;
  letter-spacing: 1.5px;
  margin-bottom: 0.75rem;
  text-transform: uppercase;
}

.main-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #14C59C;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.main-description {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  margin-bottom: 0;
}

/* Features List */
.features-list {
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  gap: 0.75rem;
}

.whiteLayer{
  background: white;
  border-radius: 50%;
  width: 11px;
  height: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}


/* Enhanced Contact Info */
.enhanced-contact-info {
  border-top: 1px solid rgba(20, 197, 156, 0.3);
  padding-top: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.contact-info-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.contact-info-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
}

.contact-info-subtitle {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  margin-bottom: 0;
}

/* Contact Methods Grid */
.contact-methods-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  flex: 1;
}

.contact-method-card {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(20, 197, 156, 0.2);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.contact-method-card:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(20, 197, 156, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.method-icon {
  width: 40px;
  height: 40px;
  /* background: linear-gradient(135deg, #14C59C, #0e9f7e); */
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  /* box-shadow: 0 3px 12px rgba(20, 197, 156, 0.3); */
}

.method-icon i {
  font-size: 1.1rem;
  color: #14C59C;
}

.method-content {
  flex: 1;
  min-width: 0;
}

.method-title {
  font-size: 1rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.2rem;
}

.method-subtitle {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0;
}

.method-label {
  font-size: 0.85rem;
  font-weight: 600;
  color: #14C59C;
  margin-bottom: 0.4rem;
  display: block;
}

.method-link {
  color: rgba(255, 255, 255, 0.9) !important;
  text-decoration: none !important;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.8rem;
  font-weight: 500;
  padding: 0.3rem 0;
  border-radius: 6px;
  transition: all 0.3s ease;
  margin-bottom: 0.2rem;
}

.method-link:last-child {
  margin-bottom: 0;
}

.method-link:hover {
  color: #14C59C !important;
  background: rgba(20, 197, 156, 0.1);
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  transform: translateX(4px);
}

.method-link i {
  font-size: 0.875rem;
  opacity: 0.8;
}

.method-link:hover i {
  opacity: 1;
  transform: scale(1.1);
}



.whatsapp-link {
  color: #25D366 !important;
}

.whatsapp-link:hover {
  background: rgba(37, 211, 102, 0.1) !important;
  color: #25D366 !important;
}

.location-link {
  flex-direction: column;
  align-items: flex-start;
  gap: 0.25rem;
}

.office-name {
  font-weight: 600;
  color: white;
}

.office-address {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}
.office-addressLine2{
  font-size: 0.65rem;
  color: #14C59C;
  line-height: 1.4;
}



.huma-icon {
  /* background: linear-gradient(135deg, #14C59C, #0e9f7e); */
  padding: 6px;
}

.huma-robo-icon {
  width: 28px;
  height: 28px;
  object-fit: contain;
  filter: brightness(0) invert(1);
}

/* Contact Details */
.contact-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.contact-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #14C59C;
}

.contact-value {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

/* Functional Contact Links */
.contact-link {
  color: rgba(255, 255, 255, 0.9) !important;
  text-decoration: none !important;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  padding: 0.25rem 0;
  border-radius: 4px;
}

.contact-link:hover {
  color: #14C59C !important;
  text-decoration: none !important;
  transform: translateX(2px);
}

.contact-link:focus {
  outline: 2px solid #14C59C;
  outline-offset: 2px;
}

.contact-icon {
  font-size: 0.875rem;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.contact-link:hover .contact-icon {
  opacity: 1;
  color: #14C59C;
  transform: scale(1.1);
}

/* Specific hover effects for different contact types */
.contact-link[href^="tel:"]:hover {
  background: rgba(20, 197, 156, 0.1);
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.contact-link[href^="mailto:"]:hover {
  background: rgba(20, 197, 156, 0.1);
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.contact-link[href*="maps"]:hover {
  background: rgba(20, 197, 156, 0.1);
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

/* Contact Form Card */
.contact-form-card {
  background: white !important;
  padding: 2rem !important;
  border-radius: 12px !important;
  height: 100% !important;
  min-height: 450px !important;
  box-shadow: 0 4px 20px rgba(14, 27, 93, 0.08) !important;
  border: 1px solid rgba(14, 27, 93, 0.1) !important;
  display: flex !important;
  flex-direction: column !important;
  visibility: visible !important;
}

/* Form Header Section */
.form-header-section {
  margin-bottom: 2rem;
}

.form-section-title {
  font-size: 2.2rem;
  font-weight: 500 !important;
  color: #0e1b5d;
  margin-bottom: 1rem;
}

.form-description {
  font-size: 1rem;
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

/* Form Benefits */
.form-benefits {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
  color: #495057;
}

.benefit-item i {
  font-size: 1rem;
  color: #0e1b5d;
  width: 16px;
  text-align: center;
}

.benefit-item span {
  font-weight: 500;
}

/* Modern Form Styling */
.modern-contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: 100%;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #223a6b;
  margin-bottom: 0.5rem;
}

.modern-input,
.modern-textarea {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 0.9rem;
  background: #ffffff;
  transition: all 0.3s ease;
  outline: none;
  font-family: inherit;
}

.modern-input:focus,
.modern-textarea:focus {
  border-color: #14C59C;
  box-shadow: 0 0 0 3px rgba(20, 197, 156, 0.1);
  transform: translateY(-1px);
}

.modern-input::placeholder,
.modern-textarea::placeholder {
  color: #9ca3af;
  font-size: 0.9rem;
}

.modern-textarea {
  resize: vertical;
  min-height: 140px;
  max-height: 200px;
  font-family: inherit;
}

/* Submit Button */
.form-submit {
  margin-top: 1.5rem;
  padding-top: 0;
}

.modern-submit-btn {
  background: #0e1b5d;
  color: white;
  border: none;
  padding: 0.875rem 2rem;
  font-size: 0.95rem;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  box-shadow: 0 4px 15px rgba(14, 27, 93, 0.3);
}

.modern-submit-btn:hover {
  background: #14C59C;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(14, 27, 93, 0.4);
}



/* Responsive Design */
@media (max-width: 991.98px) {
  .clean-modern-contact {
    padding: 3rem 0;
  }

  .contact-info-card,
  .contact-form-card {
    padding: 2rem;
  }

  .info-section-title,
  .form-section-title {
    font-size: 1.3rem;
  }

  .form-description {
    font-size: 0.95rem;
  }
}

@media (max-width: 767.98px) {
  .clean-modern-contact {
    padding: 2rem 0;
    min-height: auto;
  }

  .contact-info-card,
  .contact-form-card {
    padding: 2rem 1.5rem;
    margin-bottom: 1.5rem;
  }

  .form-section-title {
    font-size: 1.2rem;
  }

  .form-description {
    font-size: 0.9rem;
  }

  .main-title {
    font-size: 1.75rem;
  }

  .contact-us-label {
    font-size: 0.8rem;
  }

  .main-description,
  .general-contact-description {
    font-size: 0.9rem;
  }

  .contact-header {
    margin-bottom: 2rem;
  }

  .features-list {
    margin-bottom: 2rem;
  }

  .feature-item {
    margin-bottom: 0.75rem;
  }

  .feature-text {
    font-size: 0.9rem;
  }

  .contact-info-title {
    font-size: 1.3rem;
  }

  .contact-info-subtitle {
    font-size: 0.9rem;
  }

  .contact-methods-grid {
    gap: 0.75rem;
  }

  .contact-method-card {
    padding: 1rem;
  }

  .method-icon {
    width: 42px;
    height: 42px;
  }

  .method-icon i {
    font-size: 1.1rem;
  }

  .method-title {
    font-size: 1rem;
  }

  .method-subtitle {
    font-size: 0.8rem;
  }

  .method-label {
    font-size: 0.85rem;
  }

  .method-link {
    font-size: 0.85rem;
  }

  .modern-contact-form {
    gap: 1.25rem;
  }

  .modern-input,
  .modern-textarea {
    padding: 0.75rem;
    font-size: 0.85rem;
  }

  .modern-submit-btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 575.98px) {
  .contact-info-card,
  .contact-form-card {
    padding: 1.5rem 1rem;
  }

  .form-section-title {
    font-size: 1.1rem;
  }

  .main-title {
    font-size: 1.5rem;
  }

  .contact-us-label {
    font-size: 0.75rem;
    letter-spacing: 1.5px;
  }

  .main-description,
  .general-contact-description {
    font-size: 0.85rem;
  }

  .contact-header {
    margin-bottom: 1.5rem;
  }

  .features-list {
    margin-bottom: 1.5rem;
  }

  .feature-item {
    margin-bottom: 0.5rem;
  }

  .feature-check {
    width: 18px;
    height: 18px;
  }

  .feature-check i {
    font-size: 0.7rem;
  }

  .feature-text {
    font-size: 0.85rem;
  }

  .contact-info-title {
    font-size: 1.2rem;
  }

  .contact-info-subtitle {
    font-size: 0.85rem;
  }

  .enhanced-contact-info {
    padding-top: 1.5rem;
  }

  .contact-info-header {
    margin-bottom: 1.5rem;
  }

  .contact-methods-grid {
    gap: 0.5rem;
  }

  .contact-method-card {
    padding: 0.875rem;
    gap: 0.75rem;
  }

  .method-icon {
    width: 36px;
    height: 36px;
  }

  .method-icon i {
    font-size: 1rem;
  }

  .method-title {
    font-size: 0.95rem;
  }

  .method-subtitle {
    font-size: 0.75rem;
  }

  .method-label {
    font-size: 0.8rem;
    margin-bottom: 0.375rem;
  }

  .method-link {
    font-size: 0.8rem;
    gap: 0.375rem;
    padding: 0.25rem 0;
  }

  .office-name {
    font-size: 0.8rem;
  }

  .office-address {
    font-size: 0.75rem;
  }

  .modern-contact-form {
    gap: 1rem;
  }

  .form-label {
    font-size: 0.85rem;
  }

  .modern-input,
  .modern-textarea {
    padding: 0.625rem;
    font-size: 0.8rem;
  }

  .modern-submit-btn {
    padding: 0.625rem 1rem;
    font-size: 0.85rem;
  }
}

/* Enhanced Map Section - Apply4U Theme */
.map-section {
  background: #ffffff;
  padding: 4rem 0;
  border-top: 1px solid rgba(14, 27, 93, 0.1);
}

.map-header {
  margin-bottom: 2rem;
}

.map-title {
  font-size: 2rem;
  font-weight: 600;
  color: #0e1b5d;
  margin-bottom: 1rem;
}

.map-subtitle {
  font-size: 1rem;
  color: #223a6b;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
}

.map-container {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(14, 27, 93, 0.15);
  margin: 0 auto;
}

.interactive-map {
  width: 100%;
  height: 450px;
  border: none;
  display: block;
  filter: grayscale(20%);
  transition: filter 0.3s ease;
}

.interactive-map:hover {
  filter: grayscale(0%);
}

/* Map Overlay */
.map-overlay {
  position: absolute;
  top: 2rem;
  left: 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(14, 27, 93, 0.15);
  max-width: 280px;
  border: 1px solid rgba(14, 27, 93, 0.1);
}

.overlay-content {
  text-align: center;
}

.location-pin {
  width: 50px;
  height: 50px;
  background: #0e1b5d;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  box-shadow: 0 4px 15px rgba(14, 27, 93, 0.3);
}

.location-pin i {
  color: white;
  font-size: 1.2rem;
}

.map-overlay h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #0e1b5d;
  margin-bottom: 0.5rem;
}

.map-overlay p {
  font-size: 0.9rem;
  color: #223a6b;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.directions-btn {
  background: #0e1b5d;
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(14, 27, 93, 0.3);
}

.directions-btn:hover {
  background: #14C59C;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(14, 27, 93, 0.4);
  color: white;
  text-decoration: none;
}

/* Additional Info Section - Apply4U Theme */
.additional-info-section {
  background: #f6faff;
  padding: 4rem 0;
}

.info-feature-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  height: 100%;
  box-shadow: 0 4px 20px rgba(14, 27, 93, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(14, 27, 93, 0.1);
}

.info-feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(14, 27, 93, 0.15);
}

.feature-icon {
  width: 70px;
  height: 70px;
  background: #0e1b5d;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  box-shadow: 0 4px 15px rgba(14, 27, 93, 0.3);
}

.feature-icon i {
  color: #14C59C;
  font-size: 1.5rem;
}

.info-feature-card h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #0e1b5d;
  margin-bottom: 1rem;
}

.info-feature-card p {
  font-size: 0.95rem;
  color: #223a6b;
  line-height: 1.6;
  margin: 0;
}

/* Call to Action Section - Apply4U Theme */
.cta-section {
  margin-top: 3rem;
  padding: 3rem 2rem;
  background: #0e1b5d;
  border-radius: 16px;
  color: white;
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 30px 30px;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(180deg); }
}

.cta-content {
  position: relative;
  z-index: 2;
}

.cta-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
}

.cta-subtitle {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-btn {
  padding: 0.875rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.95rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  min-width: 150px;
  justify-content: center;
}

.cta-btn.primary {
  background: white;
  color: #0e1b5d;
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

.cta-btn.primary:hover {
  background: #14C59C;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 255, 255, 0.3);
  color: white;
  text-decoration: none;
}

.cta-btn.secondary {
  background: #14C59C;
  color: white;
  backdrop-filter: blur(10px);
}

.cta-btn.secondary:hover {
  background: white;
  transform: translateY(-2px);
  color: #0e1b5d;
  text-decoration: none;
}

/* Responsive Design for Additional Sections */
@media (max-width: 991.98px) {
  .map-section {
    padding: 3rem 0;
  }

  .map-title {
    font-size: 1.75rem;
  }

  .interactive-map {
    height: 400px;
  }

  .map-overlay {
    top: 1.5rem;
    left: 1.5rem;
    padding: 1.25rem;
    max-width: 250px;
  }

  .additional-info-section {
    padding: 3rem 0;
  }

  .info-feature-card {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .feature-icon {
    width: 60px;
    height: 60px;
  }

  .feature-icon i {
    font-size: 1.3rem;
  }

  .cta-section {
    padding: 2.5rem 1.5rem;
    margin-top: 2rem;
  }

  .cta-title {
    font-size: 1.75rem;
  }

  .cta-subtitle {
    font-size: 1rem;
  }
}

@media (max-width: 767.98px) {
  .map-section {
    padding: 2rem 0;
  }

  .map-title {
    font-size: 1.5rem;
  }

  .map-subtitle {
    font-size: 0.9rem;
  }

  .interactive-map {
    height: 350px;
  }

  .map-overlay {
    position: static;
    margin: 1rem auto 0;
    max-width: 100%;
    background: white;
    backdrop-filter: none;
  }

  .additional-info-section {
    padding: 2rem 0;
  }

  .info-feature-card {
    padding: 1.25rem;
  }

  .feature-icon {
    width: 50px;
    height: 50px;
  }

  .feature-icon i {
    font-size: 1.1rem;
  }

  .info-feature-card h4 {
    font-size: 1.1rem;
  }

  .info-feature-card p {
    font-size: 0.9rem;
  }

  .cta-section {
    padding: 2rem 1rem;
    border-radius: 12px;
  }

  .cta-title {
    font-size: 1.5rem;
  }

  .cta-subtitle {
    font-size: 0.95rem;
    margin-bottom: 1.5rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-btn {
    width: 100%;
    max-width: 250px;
  }
}

@media (max-width: 575.98px) {
  .map-section {
    padding: 1.5rem 0;
  }

  .map-title {
    font-size: 1.3rem;
  }

  .interactive-map {
    height: 300px;
  }

  .map-overlay {
    padding: 1rem;
  }

  .location-pin {
    width: 40px;
    height: 40px;
  }

  .location-pin i {
    font-size: 1rem;
  }

  .map-overlay h4 {
    font-size: 1rem;
  }

  .map-overlay p {
    font-size: 0.85rem;
  }

  .directions-btn {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .additional-info-section {
    padding: 1.5rem 0;
  }

  .info-feature-card {
    padding: 1rem;
  }

  .feature-icon {
    width: 45px;
    height: 45px;
    margin-bottom: 1rem;
  }

  .feature-icon i {
    font-size: 1rem;
  }

  .info-feature-card h4 {
    font-size: 1rem;
  }

  .info-feature-card p {
    font-size: 0.85rem;
  }

  .cta-section {
    padding: 1.5rem 0.75rem;
  }

  .cta-title {
    font-size: 1.25rem;
  }

  .cta-subtitle {
    font-size: 0.9rem;
  }

  .cta-btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }
}