.InterviewTips img{
  width:100%;
  height:auto;
  margin-bottom: 15px;
  border-radius: 10px;
}
.InterviewTips p {
  font-size: 16px;
}
.InterviewTips p a {
  font-weight: 500;
  color: #0e1b5d;
  text-decoration: underline;
}
.InterviewTips p a:hover {
  font-weight: 500;
  color: #0e1b5d;
  text-decoration: none;
}
.InterviewTips span {
  margin: 0;
  padding: 0;
  width: 100%;
  font-size: 18px;
  font-weight: 600!important;
}
.card-body  h2 {
  margin: 0;
  padding: 0;
  width: 100%;
  font-size: 18px;
  margin-bottom: 10px;
  font-weight: 600!important;
}
.card-body p {
  font-size: 16px;
} 
.InterviewTips h1{
  margin: 0;
  padding: 0;
  width: 100%;
  font-size: 24px;
  color: #223a6b;
  text-align: left;
  margin-bottom: 10px;
  font-weight: 600!important;
}
.card-header .title {
  font-size: 16px;
  color: #223a6b;
  font-weight: 500!important;
}
.card-header .accicon {
  float: right;
  font-size: 20px;  
  width: 1.2em;
}
.card-header{
  cursor: pointer;
  border-bottom: none;
}
.card{
  border: 1px solid #ddd;
}
.card-body{
  border-top: 1px solid #ddd;
}
.card-header:not(.collapsed) .rotate-icon {
  transform: rotate(180deg);
}