steps:
- task: NodeTool@0
  inputs:
    versionSpec: '18.20.1'
  displayName: 'Install Node.js'

- script: |
    npm install --legacy-peer-deps
  displayName: 'Install dependencies'

- script: |
    npm run stage2
  displayName: 'Build Angular App for Stage2 Environment'

- script: |
    echo "Copying main.js file to the dist/server folder"
    cp main.js $(Build.SourcesDirectory)/dist/server
  displayName: 'Copy main.js file to dist/server folder'

- task: ArchiveFiles@2
  displayName: "Create angular-app Zip Archive"
  inputs:
    rootFolderOrFile: '$(Build.SourcesDirectory)\dist'
    includeRootFolder: false
    archiveType: 'zip'
    archiveFile: '$(Build.ArtifactStagingDirectory)/angular-app.zip'
    replaceExistingArchive: true

- task: PublishBuildArtifacts@1
  inputs:
    PathtoPublish: '$(Build.ArtifactStagingDirectory)'
    ArtifactName: 'angular-app'
    publishLocation: 'Container'
  displayName: 'Publish Build Artifacts'