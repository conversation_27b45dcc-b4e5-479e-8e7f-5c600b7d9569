import { Component, OnInit } from '@angular/core';
import { LoadingService } from '@apply4u/services/loading.service';
import { ToasterMessageService } from '@apply4u/services/toaster-message.service';
import { UserService } from '@apply4u/services/user.service';
import { UseraccountsettingsService } from '@apply4u/services/useraccountsettings.service';
import { ChangePassword } from '@apply4u/models/change-password';
import { Useraccountsettings } from '@apply4u/shared/constant/useraccountsettings';
import { LocalstorageService } from '@apply4u/services/localstorage.service';
import { ProfileService } from '@apply4u/services/profile.service';
import { Shared } from '@apply4u/models/shared';
import { FileUploadService } from '@apply4u/services/fileupload.service';
import { EditCompanyService } from '@apply4u/services/editcompany.service';
import { CompanyAccount as CompanyAccount } from '@apply4u/models/companyAccount';
import { TransferJob } from '@apply4u/models/transferJob';
import { ConfirmationService } from "@apply4u/shared/components/confirmation-modal/confirmation.service";
import { CommonMessages } from '@apply4u/shared/constant/messages/common-messages';
import { NewUserCentricMesssages } from '@apply4u/shared/constant/messages/new-user-centric-header-messages';
import { UserAccountSettingMessage } from '@apply4u/shared/constant/messages/user-account-setting-messages';
import { ConfirmationMessages } from '@apply4u/shared/constant/messages/confirmation-messages';
import { ContextLevelKeys } from '@apply4u/shared/constant/Local-storage-keys/context-level-keys';
import { ContextService } from '@apply4u/services/context-service';
import { DashboardType as DashboardTypeEnum } from '@apply4u/shared/enums';
import { IsFalse, IsNotNull, IsNotNullOrEmpty, IsNotZero, IsNull, IsNullOrEmpty, IsTrue } from '@apply4u/shared/helpers/common/type-safe-helpers';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { LoginTypeKeys } from '@apply4u/shared/constant/Local-storage-keys/login-type-keys';
import { BreadCrumbs, ListItem } from '@apply4u/models/seo/bread-crumbs';
import { BreadCrumbTitleDashBoard, BreadcrumbsTitleAccountSettings } from '@apply4u/shared/constant/constant';
import { environment } from '@environment/environment';
import { AccountSetting_RouteUrl, DashBoard_RouteUrl } from '@apply4u/shared/constant/navigation/navigation';
import { GetElementById } from '@apply4u/shared/helpers/common/common-helpers';
import { BehalfOfUserService } from '@apply4u/services/behalf-of-user/behalf-of-user-service';
import { Package } from '@apply4u/models/package';
import { PackageService } from '@apply4u/services/package.Service';

@Component({
  selector: 'app-user-account-settings',
  templateUrl: './user-account-settings.component.html',
  styleUrls: ['./user-account-settings.component.css']
})
export class UserAccountSettingsComponent implements OnInit {
  ChangePassword: ChangePassword;
  IsCurrentPasswordShow: boolean = false;
  IsNewPasswordShow: boolean = false;
  IsConfirmPasswordShow: boolean = false;
  IsCandidate: boolean = false;
  IsRecruiter: boolean = false;
  IsUserBoth: boolean = false;
  IsRecruiterOnly: boolean = false;
  JobSeeker: number = Shared.JobSeeker;
  Recruiter: number = Shared.Recruiter;
  SelectedCvFile: File;
  SelectedCvFileCount: number = 0;
  UploadedResume: any;
  SelectedFiles: any;
  FileCount: number;
  FormData: FormData;
  ShowRegisterCompany: boolean = false;
  CompanyAccount: CompanyAccount = new CompanyAccount();
  TransferJobs: TransferJob[] = [];
  IsAccountSettingPage: boolean = true;
  ChangePasswordValidationForm: UntypedFormGroup;
  IsChangePasswordFormSubmitted: boolean = false;
  ContextService: ContextService;
  BreadCrumbSchema: BreadCrumbs;
  IsApplyOnMyBehalf: boolean;
  TrialApplcable: boolean;
  PackageDetails: Package;
  CurrentPakcage: boolean;

  constructor(
    private loadingService: LoadingService,
    private confirmationService: ConfirmationService,
    private toasterService: ToasterMessageService,
    private userService: UserService,
    private userAccountSettingService: UseraccountsettingsService,
    private localstorageservice: LocalstorageService,
    private profileService: ProfileService,
    private uploadfile: FileUploadService,
    private contextService: ContextService,
    private editCompanyService: EditCompanyService,
    private formBuilder: UntypedFormBuilder,
    private packageService: PackageService,
    private localStorageService: LocalstorageService,
    private behalfOfUserService: BehalfOfUserService) {
      this.ContextService = this.contextService;
      this.InitializeValidationForms();

    if (IsNull(this.ChangePassword)) {
      this.ChangePassword = new ChangePassword();
    }
  }

  InitializeValidationForms(): void {
    this.ChangePasswordValidationForm = this.formBuilder.group({
      CurrentPassword: ['', Validators.required],
      NewPassword: ['', Validators.required],
      ConfirmPassword: ['', Validators.required],
      MatchExistingPassword: [Validators.required],
      MatchNewPassword: [Validators.required],
    })
  }

  ngOnInit(): void {
    this.SetUserTypesOnOff();
   
    if (this.contextService.IsBrowser) {
      this.PopulateBreadCrumbsDisplayList();
      this.GetBehalfOfUserConsent();
      this.GetUserPackageDetail();
    }
  }

  OnExistingPasswordCheck(){
    if (IsNotNullOrEmpty(this.ChangePassword.CurrentPassword)) {
      this.userService.ValidateUserCredentials(this.ChangePassword.CurrentPassword, this.contextService.LoggedInUserId).subscribe(result => {
        if (IsTrue(result)) {
            this.ChangePasswordValidationForm.controls['MatchExistingPassword'].setErrors(null);
        } else {
          this.ChangePasswordValidationForm.controls['MatchExistingPassword'].setErrors({'required': true});
          }
        });
        }
  }

  OnNewPasswordMatch () {
    if (IsNotNullOrEmpty(this.ChangePassword.ChangedPassword)) {
      if (this.ChangePassword.ChangedPassword == this.ChangePassword.CurrentPassword) {
        this.ChangePasswordValidationForm.controls['MatchNewPassword'].setErrors({'required': true});
      } else {
        this.ChangePasswordValidationForm.controls['MatchNewPassword'].setErrors(null);
      }
    }
  }

  OnChangeClick(): void {
    if (this.contextService.IsUserLoggedIn == false) {
      this.toasterService.Error(Useraccountsettings.OnChangeClickLoggedInMessage);

      return;
    } else {
      this.IsChangePasswordFormSubmitted = true;

      if (this.ChangePasswordValidationForm.valid) {
        this.IsChangePasswordFormSubmitted = false;


          if (this.ChangePassword.ChangedPassword != this.ChangePassword.ConfirmPassword) {
            this.toasterService.Error(Useraccountsettings.OnChangeClickMatchPasswordMessage);

            return;
          }

          this.confirmationService.SetMessage(Useraccountsettings.OnPasswordChangeConfirmationMessage).OpenConfirmationDialogue(isConfirmed => {
            if (isConfirmed) {
              this.ChangePassword.Id = this.contextService.LoggedInUserId;
              this.userAccountSettingService.ChangePassword(this.contextService.LoggedInUserId, this.ChangePassword).
                subscribe(result => {
                  this.toasterService.Success(Useraccountsettings.OnChangeClickPasswordChangedMessage);
                  this.ChangePassword = new ChangePassword();

                  return;
                }, error => {
                  this.toasterService.ServerError();
                  this.ChangePasswordValidationForm.reset();
                }
                );
            }   
          });
      }
    } 
  }

  OnAccountDeleteClick(): void {
    this.loadingService.Show();

    this.userAccountSettingService.CheckUserCompanyAccount(this.contextService.LoggedInUserId).
      subscribe(result => {
        this.CompanyAccount = result;

        if (this.CompanyAccount.Members.length <= 0 && this.CompanyAccount.Jobs.length <= 0) {
          this.confirmationService.SetMessage(Useraccountsettings.OnAccountDeleteConfirmMessage).OpenConfirmationDialogue(isConfirmed => {
            if (isConfirmed) {
              this.DeleteAccount();
            }
          });
        } else {
          this.confirmationService.SetMessage(Useraccountsettings.OnCompanyAccountDeleteConfirmMessage).OpenConfirmationDialogue(isConfirmed => {
            if (isConfirmed) {
              if (this.CompanyAccount.IsCompanyUser == true && this.CompanyAccount.Members.length > 0 && this.CompanyAccount.Jobs.length > 0) {
                if (this.contextService.IsBrowser) {
                  document.getElementById("btnOpenJobConfirmationDialog").click();
                }
              } else {
                this.DeleteAccount();
              }
            }
          });
        }
        this.loadingService.Hide();
      });
  }

  DeleteAccount(): void {
    this.loadingService.Show();

    this.userAccountSettingService.DisableUserAccount(this.contextService.LoggedInUserId, this.TransferJobs).subscribe(result => {
      this.loadingService.Hide();  
      this.toasterService.Success(Useraccountsettings.OnAccountDeletedMessage);
        this.contextService.SignOutRedirect();
        return;
      });
  }

  TransferJob(jobId, userId): void {
    this.TransferJobs = this.TransferJobs.filter(d => d.JobId != jobId);
    this.TransferJobs.push({ JobId: jobId, MemberId: userId });
  }

  OnJobTransferConfirm(confirm: boolean): void {
    if (confirm == true) {
      this.DeleteAccount();
    }
  }

  MyCompanyPopup(): void {
    this.editCompanyService.IsCompanyJoinedByUser(this.contextService.LoggedInUserId).subscribe(companyName => {
      if (IsNullOrEmpty(companyName)) {
        this.ShowRegisterCompany = true;
        document.getElementById("btnRegisterCompanyModal").click();
      } else {
        this.loadingService.Show();
        this.userAccountSettingService.MarkAsRecruiter(this.contextService.LoggedInUserId, true).subscribe(result => {
          this.loadingService.Hide();
          this.IsRecruiter = true;
          this.localstorageservice.SetItem(ContextLevelKeys.IsRecruiter, "1");
          this.OnDashBoardStateChangeHanlder();
          this.toasterService.ChangesSavedSuccessfully();
        }, error => {
          this.loadingService.Hide();
          this.toasterService.ServerError();
        });
      }
    });
  }

  ShowRegisterCompanyEvent(isJoined: boolean): void {
    if (IsTrue(isJoined)) {
      this.loadingService.Show();
      this.userAccountSettingService.MarkAsRecruiter(this.contextService.LoggedInUserId, true).subscribe(result => {
        this.loadingService.Hide();
        this.IsRecruiter = true;
        this.localstorageservice.SetItem(ContextLevelKeys.IsRecruiter, "1");
        this.OnDashBoardStateChangeHanlder();
        this.toasterService.ChangesSavedSuccessfully();
      }, error => {
        this.loadingService.Hide();
        this.toasterService.ServerError();
      });
    } 
  }

  RecruiterMenuChangeHandler(value: boolean): void {
    if (this.contextService.IsUserLoggedIn) {
      if (IsTrue(value)) {
        this.MyCompanyPopup();
      } else {
        if(IsTrue(this.IsCandidate)) {
        this.loadingService.Show();
        this.userAccountSettingService.MarkAsRecruiter(this.contextService.LoggedInUserId, false).subscribe(result => {
          this.loadingService.Hide();
          this.IsRecruiter = false;
          this.localstorageservice.SetItem(ContextLevelKeys.IsRecruiter, "0");
          this.OnDashBoardStateChangeHanlder();
          this.toasterService.ChangesSavedSuccessfully();
        }, error => {
          this.loadingService.Hide();
          this.toasterService.ServerError();
        });
      } else {
        this.toasterService.Error(UserAccountSettingMessage.OnDashboardCategorySelectionError);
        this.IsRecruiter = true;
      }
      }
    } else {
      this.toasterService.Error(UserAccountSettingMessage.OnDashboardTypeChangeLoginError);
      this.contextService.SignInRedirect();
    }
  }

  CandidateMenuChangeHandler(value: boolean): void {
        if (this.contextService.IsUserLoggedIn) {
      if (IsTrue(value)) {
        this.profileService.getResumeByUserId(this.contextService.LoggedInUserId).subscribe(result => {
          if (result.length > 0) {
            this.loadingService.Show();
            this.userAccountSettingService.MarkAsCandidate(this.contextService.LoggedInUserId, true).subscribe(result => {
              this.loadingService.Hide();
              this.localstorageservice.SetItem(ContextLevelKeys.IsCandidate, "1");
              this.IsCandidate = true;
              this.IsRecruiter = false;
              this.IsRecruiterOnly = false;
              this.toasterService.ChangesSavedSuccessfully();
              this.OnDashBoardStateChangeHanlder();
            }, error => {
              this.loadingService.Hide();
              this.toasterService.ServerError();
            });
          } else {
            if (this.contextService.IsBrowser) {
              document.getElementById("OpenCVUploaderModalIdnew").click();
            }
          }
        });
      } else {
        if(IsTrue(this.IsRecruiter)) {
        this.loadingService.Show();
        this.userAccountSettingService.MarkAsCandidate(this.contextService.LoggedInUserId, false).subscribe(result => {
          this.loadingService.Hide();
          this.localstorageservice.SetItem(ContextLevelKeys.IsCandidate, "0");
          this.IsCandidate = false;
          this.IsRecruiterOnly = true;
          this.IsUserBoth = false;
          this.toasterService.ChangesSavedSuccessfully();
          this.OnDashBoardStateChangeHanlder();
        }, error => {
          this.loadingService.Hide();
          this.toasterService.ServerError();
        });
      } else {
        this.toasterService.Error(UserAccountSettingMessage.OnDashboardCategorySelectionError);
        this.IsCandidate = true;
      }
      }
    } else {
      this.toasterService.Error(UserAccountSettingMessage.OnDashboardTypeChangeLoginError);
      this.contextService.SignInRedirect();
    }
  }

  OnDashBoardStateChangeHanlder(): void {
    if (this.contextService.IsUserLoggedIn) {
      if (this.contextService.IsJobSeeker ? this.contextService.IsRecruiterTypeActivated : this.contextService.IsJobSeekerTypeActivated) {
        this.UpdateUserType(this.contextService.IsJobSeeker ? true : false);
      }
    }
  }

  UpdateUserType(isSwitchingToRecruiter: boolean): void {
    if (isSwitchingToRecruiter) {
      this.userAccountSettingService.UpdateDashboardType(this.contextService.LoggedInUserId, DashboardTypeEnum.Recruiter)
      .subscribe(result=>{
        this.IsCandidate=false;
        this.contextService.SetIsJobSeeker(false);
      },error=>{
        this.toasterService.ServerError();
      });
    } else {     
      this.userAccountSettingService.UpdateDashboardType(this.contextService.LoggedInUserId, DashboardTypeEnum.JobSeeker)
      .subscribe(result=>{
        this.contextService.SetIsJobSeeker(true);
      },er=>{
        this.toasterService.ServerError();
      });
    }


  }

  SetUserTypesOnOff(): void {
    this.IsCandidate = false;
    this.IsRecruiter = false;
    let isCandidateString = this.localstorageservice.GetItem(ContextLevelKeys.IsCandidate);
    let IsRecruiterString = this.localstorageservice.GetItem(ContextLevelKeys.IsRecruiter);

    if (IsNotZero(isCandidateString) && (isCandidateString.trim() == '1')) {
      this.IsCandidate = true;
    }
  else
    if (IsNotZero(IsRecruiterString) && (IsRecruiterString.trim() == '1')) {
      this.IsRecruiter = true;
    }

    if (IsTrue(this.IsCandidate) && IsTrue(this.IsRecruiter)) {
      this.IsUserBoth = true;
    } else if (IsTrue(this.IsRecruiter) && IsFalse(this.IsCandidate)) {
      this.IsRecruiterOnly = true;
    }
  }

  OnSelectedCVFileChange(fileEvent: any): void {
    this.SelectedCvFile = fileEvent.target.files[0];

    if (this.SelectedCvFile.size / 1024 > 1024) {
      this.toasterService.Error(CommonMessages.OnUploadFileSizeNotification);
      fileEvent.target.value = '';
    } else {
      if (IsNotNull(this.SelectedCvFile)) {
        var ext = this.SelectedCvFile.name.substring(this.SelectedCvFile.name.lastIndexOf('.') + 1);

        if (ext.toLowerCase() == "docx" || ext.toLowerCase() == "doc" || ext.toLowerCase() == "pdf" || ext.toLowerCase() == "rtf" || ext.toLowerCase() == "txt") {
          this.SelectedCvFileCount = fileEvent.target.files.length;
          if (this.SelectedCvFileCount > 0) {
            this.UploadedResume = this.SelectedCvFile;
          }
        } else {
          this.toasterService.Error(CommonMessages.OnUploadingFileNotSupported);
        }
      }
    }
  }

  OnUploadResume(): void {
    if (IsNotNull(this.UploadedResume)) {
      this.FormData = new FormData();

      this.FormData.append("CVFile", this.UploadedResume, this.UploadedResume.name);
      this.confirmationService.SetMessage(ConfirmationMessages.ConfirmationOnUploadingResume).OpenConfirmationDialogue(isConfirmed => {
        if (isConfirmed) {
          this.loadingService.Show();
          this.uploadfile.Uploadfile(this.FormData).subscribe(result => {
            this.FileCount = 0;
            this.SelectedFiles = null;
            this.loadingService.Show();
            this.profileService.updateUserResume(this.contextService.LoggedInUserId, result[0], true).subscribe(result => {
              this.userAccountSettingService.MarkAsCandidate(this.contextService.LoggedInUserId, true, true).subscribe(result => {
                this.localstorageservice.SetItem(ContextLevelKeys.IsCandidate, "1");
                this.localstorageservice.SetItem(ContextLevelKeys.IsRecruiter, "0");
                this.localstorageservice.SetItem(LoginTypeKeys.IsNewRegistration, "1");
                this.OnDashBoardStateChangeHanlder();
                this.toasterService.Success(NewUserCentricMesssages.OnUploadingResumeFileSuccess);
                this.loadingService.Hide();
                this.IsCandidate = true;
                this.toasterService.ChangesSavedSuccessfully();
                document.getElementById("OpenCVUploaderModalIdnew").click();
              });
            }, errorResult => {
              this.loadingService.Hide();
              this.toasterService.ServerError();
            });
          }, errorResult => {
            this.loadingService.Hide();
            this.toasterService.ServerError();
          });
        }
      });
    }
  }
  
  PopulateBreadCrumbsDisplayList(): void {
    this.BreadCrumbSchema = new BreadCrumbs();
    let listItem = new ListItem();
    listItem.position = 1;
    listItem.name = BreadCrumbTitleDashBoard;
    listItem.item = `${environment.HostName}${DashBoard_RouteUrl}`;
    this.BreadCrumbSchema.itemListElement.push(listItem);

    let listItem2 = new ListItem();
    listItem2.position = 2;
    listItem2.name = BreadcrumbsTitleAccountSettings;
    listItem2.item = `${environment.HostName}${AccountSetting_RouteUrl}`;
    this.BreadCrumbSchema.itemListElement.push(listItem2);
  }

  BehalfOfUserChangeHandler(userConsent: boolean): void {
    if (IsTrue(userConsent)) {
      this.loadingService.Show();
      this.behalfOfUserService.EnableBehalfOfUser(this.contextService.LoggedInUserId).subscribe({
        next: consent => {
          this.loadingService.Hide();
          this.toasterService.UpdatedSuccessfully();
          this.IsApplyOnMyBehalf = true;
          this.localStorageService.RemoveItem('IsApplyOnMyBehalf');
          this.localStorageService.SetItem("IsApplyOnMyBehalf", this.IsApplyOnMyBehalf == null ? "" : this.IsApplyOnMyBehalf.toString());
        }, error: error => {
          this.loadingService.Hide();
        }
      })
    } else if (IsFalse(userConsent)) {
      this.confirmationService.SetMessage(Useraccountsettings.OnChangeClickConfirmationMessage).OpenConfirmationDialogue(isConfirmed => {
        if (isConfirmed) {
          this.loadingService.Show();
          this.behalfOfUserService.DisableBehalfOfUser(this.contextService.LoggedInUserId).subscribe({
            next: consent => {
              this.loadingService.Hide();
              this.toasterService.UpdatedSuccessfully();
              this.IsApplyOnMyBehalf = false;
              this.localStorageService.RemoveItem('IsApplyOnMyBehalf');
              this.localStorageService.SetItem("IsApplyOnMyBehalf", this.IsApplyOnMyBehalf == null ? "" : this.IsApplyOnMyBehalf.toString());
            }, error: error => {
              this.loadingService.Hide();
            }
          })
        }
      });
    }
  }

  OnClickApplyOnMyBehalf(): void {
    if (this.contextService.IsBrowser) {
      const onUserBehalfModalId = GetElementById('btnOpenApplyOnMyBehalfDialog');
      if (IsNotNull(onUserBehalfModalId)) {
        onUserBehalfModalId.click();
      }
    }
  }

  GetBehalfOfUserConsent(): void {
    if (this.contextService.IsUserLoggedIn) {
      this.loadingService.Show();
      this.behalfOfUserService.GetBehalfOfUserById(this.contextService.LoggedInUserId).subscribe({
        next: userConsent => {
          this.loadingService.Hide();
          if (IsNotNull(userConsent)) {
            if (IsTrue(userConsent.IsActive)) {
              this.IsApplyOnMyBehalf = true;
            } else {
              this.IsApplyOnMyBehalf = false;
            }
          }
          if (IsNotNull(userConsent)) {
            if (IsTrue(userConsent.IsTrialApplicable)) {
              this.TrialApplcable = false;
            } else {
              this.TrialApplcable = true;
            }
          }
        }, error: error => {
          this.loadingService.Hide();
        }
      });
    }
  }

  GetUserPackageDetail() {
    this.PackageDetails = new Package();

    // this.loadingService.show();
    this.packageService.getUserAchivedPackage(this.contextService.LoggedInUserId, this.contextService.IsJobSeeker).subscribe({
      next: packageDetails => {
        this.PackageDetails = packageDetails[0];
        let packageName = this.PackageDetails.PackageName;

        if (IsNotNullOrEmpty(packageName)) {
          if (packageName == 'Free') {
            this.CurrentPakcage = false;
          } else {
            this.CurrentPakcage = true
          }
        }
        this.loadingService.Hide();
      },
      error: errorHandler => {
        this.loadingService.Hide();
      }
    });
  }

}
