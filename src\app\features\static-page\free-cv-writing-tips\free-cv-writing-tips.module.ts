import { NgModule } from '@angular/core';
import { CareerAdviceMenuModule } from '@apply4u/shared/components/career-advice-menu/career-advice-menu.module';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { FreeCvWrittingTipsResolver } from './free-cv-writing-tips.resolver';
import { FreeCvWrrittingTipsComponent } from './free-cv-wrritting-tips.component';
import { CareerAdviceMainModule } from '../career-advice-main/career-advice-main.module';

const routes: Routes = [
  { path: '', component: FreeCvWrrittingTipsComponent, resolve: { FreeCvWrittingTips: FreeCvWrittingTipsResolver } }
];

@NgModule({
  declarations: [
    FreeCvWrrittingTipsComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    CareerAdviceMenuModule,
    CareerAdviceMainModule
  ]
})
export class FreeCvWritingTipsModule { }
