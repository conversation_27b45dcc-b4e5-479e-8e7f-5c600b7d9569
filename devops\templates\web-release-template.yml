parameters:
- name: serviceConnectionName
  type: string
- name: webAppSVNRepoUrl
  type: string
- name: svnUsername
  type: string
- name: svnPassword
  type: string


steps:
- task: DownloadPipelineArtifact@2
  displayName: 'Download Angular App Artifact'
  inputs:
    artifactName: 'angular-app' # Matches the ArtifactName from the build pipeline
    downloadPath: '$(System.ArtifactsDirectory)/web'

- powershell: |
    choco install svn -y
  displayName: 'Install SVN via Chocolatey'

# Step 3: Extract the downloaded .zip artifact
- powershell: |
    # Define the location of the zip file and the extraction folder
    $zipFile = "$(System.ArtifactsDirectory)\web\angular-app.zip"
    $extractFolder = "$(System.ArtifactsDirectory)\web\extracted"

    # Create the extraction folder if it doesn't exist
    if (-not (Test-Path -Path $extractFolder)) {
        New-Item -Path $extractFolder -ItemType Directory
    }

    # Extract the zip file
    Expand-Archive -Path $zipFile -DestinationPath $extractFolder -Force
  displayName: 'Extract the Angular App Artifact'

# Step 4: Checkout/Export from SVN Repo
- powershell: |
    svn checkout ${{ parameters.webAppSVNRepoUrl }} $(System.ArtifactsDirectory)\svn-files --username ${{ parameters.svnUsername }} --password ${{ parameters.svnPassword }} --no-auth-cache
  displayName: 'Checkout SVN Repo'

# Step 5: Copy Files/Extract or Modify Files from Artifact to SVN Directory
- powershell: |
    # Copy the extracted files to the SVN directory
    Copy-Item -Path "$(System.ArtifactsDirectory)\web\extracted\*" -Destination "$(System.ArtifactsDirectory)\svn-files\" -Recurse -Force
  displayName: 'Copy Extracted Files to SVN Repo Directory'

- powershell: |
    cd $(System.ArtifactsDirectory)\svn-files
    svn add * --force --username $(svnUsername) --password $(svnPassword) --non-interactive
  displayName: 'SVN Add New Files'

# Step 6: Commit Changes to SVN Repo
- powershell: |
    cd $(System.ArtifactsDirectory)\svn-files
    svn commit -m "Automated commit from Azure DevOps pipeline" --username ${{ parameters.svnUsername }} --password ${{ parameters.svnPassword }}
  displayName: 'Commit Changes to SVN Repo'