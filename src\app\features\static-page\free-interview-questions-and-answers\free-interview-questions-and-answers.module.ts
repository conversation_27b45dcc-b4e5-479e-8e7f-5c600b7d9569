import { NgModule } from '@angular/core';
import { CareerAdviceMenuModule } from '@apply4u/shared/components/career-advice-menu/career-advice-menu.module';
import { RouterModule, Routes } from '@angular/router';
import { FreeInterviewQuestionsAndAnswersResolver } from './free-interview-questions-and-answers.resolver';
import { FreeInterviewQuestionAndAnswerComponent } from './free-interview-question-and-answer.component';
import { CareerAdviceMainModule } from '../career-advice-main/career-advice-main.module';

const routes: Routes = [
  { path: '', component: FreeInterviewQuestionAndAnswerComponent, resolve: { FreeInterviewQuestionAndAnswers: FreeInterviewQuestionsAndAnswersResolver } }
];


@NgModule({
  declarations: [
    FreeInterviewQuestionAndAnswerComponent
  ],
  imports: [
    RouterModule.forChild(routes),
    CareerAdviceMenuModule,
    CareerAdviceMainModule
  ]
})
export class FreeInterviewQuestionsAndAnswersModule { }
