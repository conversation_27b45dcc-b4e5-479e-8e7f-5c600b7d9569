parameters:
- name: serviceConnectionName
  type: string
- name: serverIP
  type: string
- name: serverUsername
  type: string
- name: serverPassword
  type: string
- name: webAppWorkingDirectory
  type: string
- name: webAppPoolName
  type: string

steps:
- powershell: |
    # Ensure WinRM is running and trusted hosts are set
    Restart-Service WinRM
    Set-Item wsman:\localhost\client\trustedhosts -Value ${{ parameters.serverIP }} -Force
  displayName: 'Configure WinRM for Remote Access'

- powershell: |
    # Assign parameters to variables
    $serverIP = "${{ parameters.serverIP }}"
    $username = "${{ parameters.serverUsername }}"
    $password = "${{ parameters.serverPassword }}"
    $workingDir = "${{ parameters.webAppWorkingDirectory }}"
    $appPool = "${{ parameters.webAppPoolName }}"

    # Secure password
    $securePassword = ConvertTo-SecureString $password -AsPlainText -Force
    $credential = New-Object System.Management.Automation.PSCredential ($username, $securePassword)
    
    
    # Invoke remote command
    Invoke-Command -ComputerName $serverIP -Credential $credential -ScriptBlock {
        param ($workingDir, $appPool)
        Import-Module WebAdministration
        Stop-WebAppPool -Name $appPool

        # Wait until the app pool is fully stopped before proceeding
        $appPoolStatus = (Get-WebAppPoolState -Name $appPool).Value
        while ($appPoolStatus -ne 'Stopped') {
            Write-Host "Waiting for the app pool '$appPool' to stop..."
            Start-Sleep -Seconds 5  # Wait for 5 seconds before checking again
            $appPoolStatus = (Get-WebAppPoolState -Name $appPool).Value
        }
        Write-Host "App pool '$appPool' has stopped."

        # Add SVN to the path
        $env:PATH += ";C:\Program Files\TortoiseSVN\bin"

        # Navigate to the working directory and update
        Set-Location -Path $workingDir
        svn cleanup
        svn update

        # Recycle the application pool
        
        Start-WebAppPool -Name $appPool
    } -ArgumentList $workingDir, $appPool
  displayName: 'Update SVN and Recycle App Pool'