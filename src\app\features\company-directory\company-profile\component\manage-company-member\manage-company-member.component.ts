import { Component, OnInit, Input, OnChanges, SimpleChanges, ChangeDetectionStrategy } from '@angular/core';
import { EditCompanyService } from '@apply4u/services/editcompany.service';
import { CompanyMembers } from '@apply4u/models/companyMembers';
import { LoadingService } from '@apply4u/services/loading.service';
import { ToasterMessageService } from '@apply4u/services/toaster-message.service';
import { Cvsearch4urequestService } from '@apply4u/services/cvsearch4urequest.service';
import { Cvsearch4urequest } from '@apply4u/models/cvsearch4urequest';
import { CvSearchRequestPermission } from '@apply4u/models/cv-search-request-permission';
import { forkJoin } from 'rxjs';
import { Shared } from "@apply4u/models/shared";
import { UserConnectionService } from "@apply4u/services/userConnection.service";
import { UserConnection } from '@apply4u/models/userConnection';
import { Router } from "@angular/router";
import { PackageService } from '@apply4u/services/package.Service';
import { CommonMessages } from '@apply4u/shared/constant/messages/common-messages';
import { ConfirmationMessages } from '@apply4u/shared/constant/messages/confirmation-messages';
import { ManageCompanyMemberMessages } from '@apply4u/shared/constant/messages/manage-company-member-messages';
import { ContextService } from '@apply4u/services/context-service';
import { IsAny, IsNotNull, IsNotNullOrEmpty, IsNotZero, IsNotZeroNumber, IsNull, IsNullOrEmpty, IsTrue, IsZeroNumber } from '@apply4u/shared/helpers/common/type-safe-helpers';
import { ConfirmationService } from '@apply4u/shared/components/confirmation-modal/confirmation.service';
import { ToLowerCase } from '@apply4u/shared/helpers/common/string-helpers';
import { IsUrlHostNameExists } from '@apply4u/shared/helpers/common/common-helpers';
import { CompanyInvitation } from '@apply4u/models/companyInvitation';
import { Companyjoinrequest } from '@apply4u/models/companyjoinrequest';
import { CompanyProfileMessages } from '@apply4u/shared/constant/messages/company-profile-messages';
import { EventsService } from '@apply4u/services/events.service';

@Component({
  selector: 'app-manage-company-member',
  templateUrl: './manage-company-member.component.html',
  styleUrls: ['./manage-company-member.component.css']  
})
export class ManageCompanyMemberComponent implements OnInit, OnChanges {

  CompanyMember: CompanyMembers[];
  CvSearch4URequests: Cvsearch4urequest[] = [];
  CvSearchRequestPermissions: CvSearchRequestPermission[] = [];
  JoinRequests: Companyjoinrequest[] = [];
  CompanyPendingInvitations: CompanyInvitation[] = [];
  InviteMemberModalId: number;
  LoggedInUserId: number = 0;
  ShowMembers: boolean = false;
  IsCompanyViewedDelete: boolean = false;
  IsCompanyMember: boolean = false;
  DefaultUrl: string = Shared.baseUrl;
  SelectedUserIdForPermissionManagement: number;
  SelectedUserEmailForPermissionManagement: string;
  ContextService: ContextService;

  @Input() isNewBoxStyleNeeded: boolean = false;
  @Input() CompanyId: number;
  @Input() OwnerId: number;
  @Input() isReadOnly: boolean = false;
  @Input() CompanyTitle: string = "";

  constructor(private companyService: EditCompanyService,
    private loadingService: LoadingService,
    private toasterService: ToasterMessageService,
    private cvSearchRequestService: Cvsearch4urequestService,
    private userConnectionService: UserConnectionService,
    private router: Router,
    private packageService: PackageService,
    private contextService: ContextService,
    private confirmationService: ConfirmationService,
    private eventService: EventsService,) {
    this.LoggedInUserId = this.contextService.LoggedInUserId;
    this.ContextService = this.contextService;
  }

  ngOnInit() {
    var isCompanyViewed = this.router.url.match(/detail/g);

    if (IsNotNull(isCompanyViewed)) {
      this.IsCompanyViewedDelete = true;
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if(IsNotNull(changes['CompanyId'])){
      this.CompanyId = changes['CompanyId'].currentValue;      
    }  
    
    if(IsNotNull(changes['OwnerId'])){
      this.OwnerId = changes['OwnerId'].currentValue;
    }

    if(IsNotNull(changes['isNewBoxStyleNeeded'])){
      this.isNewBoxStyleNeeded = changes['isNewBoxStyleNeeded'].currentValue;
    }

    if(IsNotNull(changes['isReadOnly'])){
      this.isReadOnly = changes['isReadOnly'].currentValue;
    }

    if(IsNotNull(changes['CompanyTitle'])){
      this.CompanyTitle = changes['CompanyTitle'].currentValue;
    }

    if (IsNotZeroNumber(this.CompanyId)) {     
      this.GetCompanyMemberInformation();
    }
  }

  private GetCompanyMemberInformation() {
    let companyMembers = this.companyService.getCompanyMembers(this.CompanyId, this.LoggedInUserId);
    let companyInvitation = this.companyService.SearchCompanyInvitations(this.CompanyId, null, null, null, null, null, null, 1, null, null);
    let companyJoinRequest = this.companyService.GetCompanyJoinRequestsByCompanyId(this.CompanyId);
    this.loadingService.Show();

    forkJoin([companyMembers, companyInvitation, companyJoinRequest]).subscribe({
      next: mappedResult => {
        this.loadingService.Hide();
        if(IsAny(mappedResult)){
          //console.log(mappedResult);
          this.SetUpCompanymembers(mappedResult[0]);
          this.LoadCompanyInvitations(mappedResult[1]);
          this.SetUpCompanyJoinRequest(mappedResult[2]);          
        }
      }, error: error => {
        this.loadingService.Hide();
      }
    });
  }

  private SetUpCompanymembers(companyMembers: CompanyMembers[]){
    if (IsAny(companyMembers)) {
      this.CompanyMember = companyMembers;
      let currentMember = this.CompanyMember.filter(m => m.UserId == this.LoggedInUserId);

      this.ShowMembers = (IsAny(currentMember));
      this.CompanyMember.forEach(element => {
        if (IsNotNull(element)) {
          if (IsNotNullOrEmpty(element.ProfileImagePath)) {
            if (IsUrlHostNameExists(element.ProfileImagePath) == false) {
              element.ProfileImagePath = this.DefaultUrl + element.ProfileImagePath;
              element.ProfileImagePath = encodeURI(element.ProfileImagePath);
            }
          } else {
            element.ProfileImagePath = "assets/images/default-staff.png";
          }

          this.CalculateMembersAvailableCredits(element);
          element.UserProfileRedirectedUrl = this.GenerateUsersProfileViewURL(element.SystemUser.UserProfile);
        }
      });

      let isCompanyMemberExists = this.CompanyMember.filter(e => e.UserId == this.contextService.LoggedInUserId);

      if (IsAny(isCompanyMemberExists)) {
        this.IsCompanyMember = true;
      }
    }
  }

  private LoadCompanyInvitations(companyInvitation: CompanyInvitation[]) {
    if (IsAny(companyInvitation)) {
      this.CompanyPendingInvitations = [];
      this.CompanyPendingInvitations = companyInvitation;
    }
  }

  SetUpCompanyJoinRequest(joinRequest: Companyjoinrequest[]) {
    if (IsAny(joinRequest)) {
      this.JoinRequests = [];
      joinRequest.forEach(element => {
        if (IsNotNull(element)) {
          if (IsNotNull(element.IsActive) && element.IsActive == true && IsNotNull(element.IsApproved) && element.IsApproved == false) {
            if (IsNotNull(element.RequestedByProfile)) {
              if (IsNullOrEmpty(element.RequestedByProfile.FirstName)) {
                element.RequestedByProfile.FirstName = "";
              }

              if (IsNullOrEmpty(element.RequestedByProfile.MiddleName)) {
                element.RequestedByProfile.MiddleName = "";
              }

              if (IsNullOrEmpty(element.RequestedByProfile.LastName)) {
                element.RequestedByProfile.LastName = "";
              }

              if (IsNotZeroNumber(element.RequestedByProfile.UserId)) {
                let combinedUserName = element.RequestedByProfile.FirstName + ' ' + element.RequestedByProfile.MiddleName + ' ' + element.RequestedByProfile.LastName;

                element.RequestedByProfile.UserProfileRedirectedUrl = this.CreateUserProfileUrl(combinedUserName, element.RequestedByProfile.UserId);
              }
            }

            this.JoinRequests.push(element);
          }

          element.IsAdmin = true;
        }
      });
    }
  }

  private CreateUserProfileUrl(username: string, userid: number): string {
    let createdUrl = "/users/";

    if (IsNotNullOrEmpty(username) && IsNotZeroNumber(userid)) {
      createdUrl += this.GetFormattedUrlString(username);
      createdUrl += '-' + userid;
      createdUrl = createdUrl.toLowerCase();
    }
    return createdUrl;
  }

  private LoadRequestPermissionsForUser(userId: number, userEmail: string) {
    this.loadingService.Show();
    this.CvSearchRequestPermissions = [];
    if (IsAny(this.CvSearch4URequests) && IsNotZeroNumber(userId)) {
      this.cvSearchRequestService.GetCvSearchRequestPermissions(null, userId, null, null)
        .subscribe(requestPermissions => {
          if (IsNull(requestPermissions)) {
            requestPermissions = [];
          }

          this.CvSearch4URequests.forEach(element => {
            let permissions = requestPermissions.filter(m => m.UserId == userId && m.CvSearchRequestId == element.Id);

            if (IsAny(permissions)) {
              let requestPermission: CvSearchRequestPermission = permissions[0];

              requestPermission.CvSearchRequest = element;
              this.CvSearchRequestPermissions.push(requestPermission);
            } else {
              let requestPermission: CvSearchRequestPermission = new CvSearchRequestPermission();

              requestPermission.CvSearchRequest = element;
              requestPermission.CvSearchRequestId = element.Id;
              requestPermission.UserId = userId;
              requestPermission.IsNotificationAllowed = false;
              requestPermission.IsViewAllowed = false;
              this.CvSearchRequestPermissions.push(requestPermission);
            }
          });
          this.loadingService.Hide();
          this.SetDataAndOpenModal(userId, userEmail);
        }, e => {
          this.loadingService.Hide();
        });
    } else {
      this.toasterService.Error(null, ManageCompanyMemberMessages.OnLoadingCVSearchRequestError);
      this.loadingService.Hide();
    }
  }

  private GetFormattedUrlString(searchWords: string) {
    searchWords = searchWords.replace(/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/g, " ");
    searchWords = searchWords.replace(/\s+/g, ' ');
    searchWords = searchWords.trim();
    searchWords = searchWords.replace(/\s/g, "-");

    return searchWords;
  }

  private SetDataAndOpenModal(userId: number, userEmail: string) {
    if (this.contextService.IsBrowser && IsNotZeroNumber(userId)) {
      this.SelectedUserIdForPermissionManagement = userId;

      if (IsNotNullOrEmpty(userEmail)) {
        this.SelectedUserEmailForPermissionManagement = userEmail;
      }

      var modelBtn = document.getElementById("btnCvSearchRequestPermissionsDialogue");

      if (IsNotNull(modelBtn)) {
        modelBtn.click();
      }
    }
  }

  private CalculateMembersAvailableCredits(element: any) {
    if (IsNotNull(element) && IsNotNullOrEmpty(element.UserCredit)) {
      let TotalCVCount = element.UserCredit.TotalCvCount;
      let TotalCvCountConsumed = element.UserCredit.TotalCvCountConsumed;
      let totalCredits = "Total credits :(" + TotalCvCountConsumed.toString() + '/' + TotalCVCount.toString() + ')';

      element.CreditsText = totalCredits;
    }
  }

  private GenerateUsersProfileViewURL(userprofile: any): string {
    let autocreatedurlparam: string = "";

    if (IsNotNull(userprofile)) {
      if (IsNotNullOrEmpty(userprofile.FirstName)) {
        autocreatedurlparam += userprofile.FirstName.trim() + " ";
      }

      if (IsNotNullOrEmpty(userprofile.LastName)) {
        autocreatedurlparam += userprofile.LastName.trim() + " ";
      }

      if (IsNotNull(userprofile.CurrentCompany)) {
        if (IsNotNullOrEmpty(userprofile.CurrentCompany.JobTitle)) {
          autocreatedurlparam += userprofile.CurrentCompany.JobTitle.trim() + " ";
        }
      }

      if (IsNotNull(userprofile.DefaultAddress)) {
        if (IsNotNullOrEmpty(userprofile.DefaultAddress.City)) {
          autocreatedurlparam += " in " + userprofile.DefaultAddress.City.trim() + " ";
        } else if (IsNotNullOrEmpty(userprofile.DefaultAddress.Address1)) {
          autocreatedurlparam += " in " + userprofile.DefaultAddress.Address1.trim() + " ";
        } else if (IsNotNullOrEmpty(userprofile.DefaultAddress.County)) {
          autocreatedurlparam += " in " + userprofile.DefaultAddress.County.trim() + " ";
        }
      }

      if (IsNotNull(userprofile.UserId)) {
        autocreatedurlparam += userprofile.UserId;
      }

      if (IsNotNullOrEmpty(autocreatedurlparam)) {
        if (autocreatedurlparam.toString().includes(",")) {
          autocreatedurlparam = autocreatedurlparam.toString().trim().split(',').join(' ');
        }

        if (autocreatedurlparam.toString().includes("  ")) {
          autocreatedurlparam = autocreatedurlparam.toString().trim().split('  ').join(' ');
        }

        if (autocreatedurlparam.toString().includes(" ")) {
          autocreatedurlparam = autocreatedurlparam.toString().trim().split(' ').join('-');
        }

        autocreatedurlparam = autocreatedurlparam.replace(/[^0-9a-zA-Z-]/g, "");
        return autocreatedurlparam.toString().toLowerCase();
      }
    }
    return;
  }

  private UpdateConnection(connection: UserConnection, action: string) {
    if (IsNotNull(connection)) {

      if (IsAny(this.CompanyMember)) {
        let findConnectedUser = this.CompanyMember.filter(connect => connect.UserId == connection.ConnectedToUserId);
        if (action == 'connect') {
          let userConnection = new UserConnection();

          if (IsAny(findConnectedUser)) {
            userConnection = connection;
            findConnectedUser[0].UserConnection = userConnection;
          }
        } else if (action == 'delete') {
          if (IsAny(findConnectedUser)) {
            findConnectedUser[0].UserConnection = null;
          }
        }
      }
    }
  }

  DeleteCompanyInvitation(invitation: any) {
    this.confirmationService.SetMessage(ConfirmationMessages.Areyousureyouwanttodeleteinvitation).OpenConfirmationDialogue(isConfirmed => {
      if (isConfirmed) {
        this.loadingService.Show();
        invitation.IsActive = false;
        this.companyService.UpdateInvitation(invitation.Id, invitation).subscribe({
          next: invite => {
            this.loadingService.Hide();
            this.toasterService.Success(CompanyProfileMessages.OnCompanyInvitationDeletedSuccess, null);
            if (IsAny(this.CompanyPendingInvitations)) {
              const index = this.CompanyPendingInvitations.indexOf(invitation);
              if (index !== -1) {
                this.CompanyPendingInvitations.splice(index, 1);
              }
            }
          }, error: error => {
            this.loadingService.Hide();
          }
        });
      }
    })
  }

  ResendCompanyInvitationNotification(companyInvitation: CompanyInvitation) {
    if (IsNotNull(companyInvitation)) {
      this.loadingService.Show();
      this.companyService.SendInvitationEmail(companyInvitation).subscribe({
        next: invite => {
          this.loadingService.Hide();
          this.toasterService.Success(null, CompanyProfileMessages.OnInvitationNotificationSendSuccess);
        }, error: error => {
          this.loadingService.Hide();
          this.toasterService.ServerError();
        }
      });
    }
  }

  OnApproveCompanyJoinRequest(joinrequest: Companyjoinrequest) {
    this.confirmationService.SetMessage(ConfirmationMessages.Areyousureyouwanttoaddthismember)
      .OpenConfirmationDialogue(isConfirmed => {
        if (isConfirmed) {
          if (joinrequest && !joinrequest.IsApproved) {
            let member = new CompanyMembers();
            member.ApprovedBy = this.contextService.LoggedInUserId;
            member.ApprovedOn = new Date();
            member.CompanyId = joinrequest.CompanyId;
            member.IsActive = true;
            member.IsAdmin = joinrequest.IsAdmin;
            member.IsApproved = true;
            member.UserId = joinrequest.RequestedBy;
  
            this.CompanyId = joinrequest.CompanyId;
            this.loadingService.Show();
            this.companyService.SaveCompanyMember(member).subscribe({
              next: () => {
                this.loadingService.Hide();
                joinrequest.IsActive = false;
                joinrequest.IsApproved = true;
                joinrequest.ApprovedBy = this.contextService.LoggedInUserId;
                joinrequest.ModifiedOn = new Date();
                const index = this.JoinRequests.indexOf(joinrequest);
                if (index > -1) {
                  this.JoinRequests.splice(index, 1);
                }
  
                this.companyService.UpdateJoinRequest(joinrequest.Id, joinrequest).subscribe({
                  next: () => {
                    this.toasterService.Success(CompanyProfileMessages.OnTeamMemberRequestApprovalSuccess, null);
                    if (this.CompanyId) {
                      this.GetCompanyMemberInformation();
                    }
                  },
                  error: () => this.loadingService.Hide()
                });
              },
              error: () => this.loadingService.Hide()
            });
          } else {
            this.toasterService.Error("Member already exists");
          }
        }
      });
  }
  

  OnRejectCompanyJoinRequest(joinrequest: Companyjoinrequest) {
    this.confirmationService.SetMessage(ConfirmationMessages.Areyousureyouwanttorejecthisrequest).OpenConfirmationDialogue(isConfirmed => {
      if (isConfirmed) {
        this.loadingService.Show();
        joinrequest.IsActive = false;
        joinrequest.IsApproved = false;
        joinrequest.ModifiedBy = this.contextService.LoggedInUserId;
        joinrequest.ModifiedOn = new Date();
        this.CompanyId = joinrequest.CompanyId;

        this.companyService.UpdateJoinRequest(joinrequest.Id, joinrequest).subscribe({
          next: request => {
            this.loadingService.Hide();
            this.toasterService.Success(CompanyProfileMessages.OnRejectionTeamMemberRequest, null);
            if (IsNotZero(this.CompanyId)) {
              this.GetCompanyMemberInformation();
            }
          }, error: error => {
            this.loadingService.Hide();
          }
        });
      }
    });
  }

  OnManagePermissionsClick(userId: number, userEmail: string) {
    if (IsNotZeroNumber(this.contextService.LoggedInUserId)) {
      this.loadingService.Show();
      this.cvSearchRequestService.GetCVSearch4uRequests(this.contextService.LoggedInUserId).subscribe({
        next: requests => {
          this.loadingService.Hide();

          if (IsAny(requests)) {
            this.CvSearch4URequests = requests;
            this.LoadRequestPermissionsForUser(userId, userEmail);
          }else {
            this.toasterService.Error(null, ManageCompanyMemberMessages.OnLoadingCVSearchRequestError);
          }
        }, error: error => {
          this.loadingService.Hide();
        }
      });
    }
  }

  OnSetAsAdmin(event: any, memberObj: CompanyMembers) {
    this.loadingService.Show();

    if (event.target.checked) {
      memberObj.SystemUser = null;
      memberObj.IsAdmin = true;

      this.companyService.updateCompanyMember(memberObj.Id, memberObj)
        .subscribe(result => {
          this.loadingService.Hide();

          if(IsNotZero(memberObj.CompanyId)){
            this.GetCompanyMemberInformation();
          }
        }, errorResult => {
          this.loadingService.Hide();
        });
    } else {
      memberObj.SystemUser = null;
      memberObj.IsAdmin = false;

      this.companyService.updateCompanyMember(memberObj.Id, memberObj)
        .subscribe(result => {
          this.loadingService.Hide();
           if(IsNotZero(memberObj.CompanyId)){
            this.GetCompanyMemberInformation();
          }
        }, errorResult => {
          this.loadingService.Hide();
        });
    }
  }

  OnMemberRemove(memberobj: CompanyMembers) {
    this.confirmationService.SetMessage(ConfirmationMessages.ConfirmationOnDeleteCompanyMember).OpenConfirmationDialogue(isConfirmed => {
      if (isConfirmed) {
        this.loadingService.Show();
        memberobj.SystemUser = null;
        memberobj.IsActive = false;

        this.companyService.DeleteCompanyMember(memberobj.Id)
          .subscribe(result => {
            if (memberobj.UserId == this.LoggedInUserId) {
              if (IsNotNullOrEmpty(this.CompanyTitle)) {
                let companyname = this.GetFormattedUrlString(this.CompanyTitle);
                let companyUrl = "/companies/" + companyname + '-profile' + '-' + this.CompanyId;

                this.router.navigateByUrl(ToLowerCase(companyUrl));
              }
            }

            this.loadingService.Hide();
            
            if(IsNotZero(memberobj.CompanyId)){
              this.GetCompanyMemberInformation();
            }
          }, errorResult => {
            this.loadingService.Hide();
          });
      }
    });
  }

  OnSaveCvSearchRequestPermission() {
    this.confirmationService.SetMessage(ConfirmationMessages.ConfirmationOnSaveSearchrequest).OpenConfirmationDialogue(isConfirmed => {
      if (isConfirmed) {
        this.loadingService.Show();
        let requestArray: any[] = [];

        this.CvSearchRequestPermissions.forEach(element => {
          if (IsNotNull(element)) {
            if (IsZeroNumber(element.Id)) {
              requestArray.push(this.cvSearchRequestService.SaveCvSearchRequestPermission(element));
            } else {
              requestArray.push(this.cvSearchRequestService.UpdateCvSearchRequestPermission(element.Id, element));
            }
          }
        });

        forkJoin(requestArray).subscribe(results => {
          this.loadingService.Hide();
          this.toasterService.Success(CommonMessages.OnSaveSuccessfully, null);

          if (this.contextService.IsBrowser) {
            var btnExit = document.getElementById("btnCvSearchRequestPermissionsModalExit");

            if (IsNotNull(btnExit)) {
              btnExit.click();
            }
          }
        }, e => {
          this.loadingService.Hide();
          this.toasterService.Error(null, CommonMessages.AnErrorOccuredwhileProcessing);
        });
      }
    });
  }

  OnAddMember() {
    if (this.CompanyId == 0) {
      this.toasterService.Error(null, ManageCompanyMemberMessages.OnRegistringCompanyFirstError);
    } else {
      if (IsNotZeroNumber(this.CompanyId)) {
        this.companyService.showCompanyInvitationModal(this.CompanyId);
        this.companyService.companyIdChangeEvent.emit(this.CompanyId);
      }
    }
  }

  ConnectToUserclickHandler(connectToUserId: number, connectionRequest: UserConnection) {
    if (this.contextService.IsUserLoggedIn) {
      this.loadingService.Show();
      this.userConnectionService.GetUserConnectedAsync(this.contextService.LoggedInUserId, connectToUserId).subscribe({
        next: connection => {
          this.loadingService.Hide();
          if (IsAny(connection)) {
            if (connection[0].StatusId == 1) {
              this.toasterService.Error(null, CommonMessages.OnUserConnectionRequestAlreadyConnected);
            } else if (connection[0].StatusId == 2) {
              this.toasterService.Error(null, CommonMessages.OnUserConnectionRequestRejected);
            } else if (connection[0].StatusId == 3) {
              this.toasterService.Error(null, CommonMessages.OnUserConnectionRequestAlreadyRequested);
            } else if (connection[0].StatusId == 4) {
              this.toasterService.Error(null, CommonMessages.OnUserConnectionRequestBlocked);
            }
          } else {
            let userConnection = new UserConnection();

            userConnection.Id = 0;
            userConnection.UserId = this.contextService.LoggedInUserId;
            userConnection.ConnectedToUserId = connectToUserId;
            userConnection.StatusId = 3;// requested 
            userConnection.IsActive = true;
            userConnection.InvitedOn = new Date();

            this.userConnectionService.SaveUserConnectionAsync(userConnection)
              .subscribe(saveResults => {
                this.loadingService.Hide();
                this.toasterService.Success(null, CommonMessages.OnUserConnectionRequestSuccess);
                userConnection.Id = saveResults;
                this.UpdateConnection(userConnection, 'connect');

              }, error => {
                this.loadingService.Hide();
                this.toasterService.ServerError();
              });
          }
        }, error: error => {
          this.loadingService.Hide();
        }
      });

    } else {
      this.toasterService.Error(null, CommonMessages.OnUserNotLoggedInError);
    }
  }

  OnDeleteConnectionRequest(connection: UserConnection) {
    if (IsNotNull(connection) && IsNotZeroNumber(connection.Id)) {
      this.confirmationService.SetMessage(ConfirmationMessages.ConfirmationOnDeleteCommon).OpenConfirmationDialogue(isConfirmed => {
        if (isConfirmed) {
          let userConnection = connection;
          this.loadingService.Show();
          this.userConnectionService.DeleteConnnectionAsync(connection.Id).subscribe({
            next: connection => {
              this.loadingService.Hide();
              this.toasterService.DeletedSuccessfully();
              this.UpdateConnection(userConnection, 'delete');
            }, error: error => {
              this.loadingService.Hide();
            }
          });
        }
      });
    }
  }

  RedirectUserToMessage(connectedId: number): void {
    // redirect user to messages page.
    this.router.navigateByUrl('/messaging/' + connectedId);
  }

  ngAfterViewInit(): void {
    this.CompanyInvitationSentSuccess();
  }

  CompanyInvitationSentSuccess(): void {
    this.eventService.OnCompanyInvitationSent.subscribe((isSent) => {
      if (IsTrue(isSent)) {
        if (IsNotZero(this.CompanyId)) {
          this.GetCompanyMemberInformation();
        }
      }
    });
  }

}
