import { Injectable } from "@angular/core";
import { ActivatedRouteSnapshot, RouterStateSnapshot } from "@angular/router";
import { MetaWrapperService } from "@apply4u/services/meta-wrapper.service";
import { GetURLWithoutQueryParams } from "@apply4u/shared/helpers/seo-helper";
import { LiveHostName, Shared } from "@apply4u/models/shared";
import { BreadCrumbs, ListItem } from "@apply4u/models/seo/bread-crumbs";

@Injectable({
  providedIn: 'root'
})
export class FreeInterviewQuestionsAndAnswersResolver  {

  constructor(private metaService: MetaWrapperService) {
  }

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): void {
    this.metaService.CreateCanonicalURL(GetURLWithoutQueryParams(`${LiveHostName}${state.url}`));
    this.metaService.SetPageTitle("Free interview Questions and Answers | Interview Tips | Apply4U");
    this.metaService.UpdateMetaTag('description', "Here you will find the most common and important interview questions with best answers to ace your interview. Discover the tips to answers the questions that will be ask in almost every interview");
    this.AddBreadCrumbsScript();
  }
  
  AddBreadCrumbsScript(): void {
    const breadcrumbSchema = new BreadCrumbs();
    let listItem = new ListItem();
    listItem.position = 1;
    listItem.name = "Home";
    listItem.item = `${Shared.ClientSideUrl}`;

    breadcrumbSchema.itemListElement.push(listItem);
    let listItem2 = new ListItem();
    listItem2.position = 2;
    listItem2.name = "free-interview-question-answer";
    listItem2.item = `${Shared.ClientSideUrl}${"https://www.apply4u.co.uk/free-interview-question-answer" }`;
    breadcrumbSchema.itemListElement.push(listItem2);
    let breadcrumbSchemaString = JSON.stringify(breadcrumbSchema);
    this.metaService.CreateBreadCrumbsScript(breadcrumbSchemaString);
  }
}
