import { Component, OnInit, HostListener, Inject, Renderer2 } from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { CheckPlatformService } from '@apply4u/services/check-platform.service';
import { BreadCrumbs, ListItem } from '@apply4u/models/seo/bread-crumbs';
import { MetaWrapperService } from '@apply4u/services/meta-wrapper.service';
import { CareerAdvice_RouteUrl, MyInterView_RouteUrl } from '@apply4u/shared/constant/navigation/navigation';
import { environment } from '@environment/environment';

@Component({
  selector: 'app-free-interview-question-and-answer',
  templateUrl: './free-interview-question-and-answer.component.html',
  styleUrls: ['./free-interview-question-and-answer.component.css']
})

export class FreeInterviewQuestionAndAnswerComponent implements OnInit {
  BreadCrumbSchema = new BreadCrumbs();

  constructor(private metaService: MetaWrapperService) {
  }

  ngOnInit(): void {
    this. PopulateBreadCrumbsDisplayList();
  }
  
  PopulateBreadCrumbsDisplayList(): void {
    let listItem = new ListItem();
    listItem.position = 1;
    listItem.name = "Career Advice Home ";
    listItem.item = `${environment.HostName}${CareerAdvice_RouteUrl}`;
    this.BreadCrumbSchema.itemListElement.push(listItem);
    
    let listItem2 = new ListItem();
    listItem2.position = 2;
    listItem2.name = "Your Interview";
    listItem2.item = `${environment.HostName}${MyInterView_RouteUrl}`;
    this.BreadCrumbSchema.itemListElement.push(listItem2);

    let listItem3 = new ListItem();
    listItem2.position = 3;
    listItem3.name = "FAQs";
    listItem3.item = `${environment.HostName}${"/free-interview-question-answer"}`;
    this.BreadCrumbSchema.itemListElement.push(listItem3);
    
    let breadcrumbSchemaString = JSON.stringify(this.BreadCrumbSchema); 
    this.metaService.CreateBreadCrumbsScript(breadcrumbSchemaString);
     }
}








